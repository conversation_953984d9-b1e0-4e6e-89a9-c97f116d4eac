import { gql } from "@apollo/client";

export const GET_ALL_PROPOSALS = gql`
    query GetAllProposals($status: QuoteStatus) {
        quotesGet(
            filters: {
                status: $status
            }
        ) {
            id
            description
            customer {
                id
                ...on CustomerBusiness {
                    basicDetails {
                        legalName
                    }
                }
                ...on CustomerIndividual {
                    basicDetails {
                        contactDetails {
                            name
                        }
                    }
                }
            }
            status
            version
            assignments {
                salesExecutive {
                    id
                    name
                }
                customerSuccessManger {
                    id
                    name
                }
            }
            date {
                validFrom
                validTill
            }
            currency
            quoteTotalSellingPrice {
                value
                currency
            }
            quoteTotalTaxAmount {
                value
                currency
            }
            quoteTotalDiscountAmount {
                value
                currency
            }
        }
    }
`;