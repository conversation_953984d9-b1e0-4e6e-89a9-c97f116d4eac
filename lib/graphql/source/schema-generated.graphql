schema {
  query: Query
  mutation: Mutation
}
""
scalar Date
""
scalar DateTime
""
scalar _FieldSet
type Amount {
  value: Float!
  currency: Currency!
}
type BusinessCustomerBasicDetails {
  legalName: String!
  website: String!
  size: EntitySize!
  industry: String
  referralSource: String
  address: String
  contactDetails: ContactDetails!
}
type Company {
  id: ID!
  name: String
  status: CompanyStatus
  onboarding: CompanyOnboarding
  users: [CompanyUser]
  basicDetails: CompanyBasicDetails
}
type CompanyBasicDetails {
  name: String!
  address: String
  phoneNumber: String
  email: String!
  website: String!
  industry: String
  size: EntitySize
}
type CompanyEmail implements Email {
  template: CompanyEmailTemplate!
  subject: String
  " optional as the template can support this as well"
  body: String
  " optional as the template can support this as well"
  recipients: [String!]!
  cc: [String!]
  bcc: [String!]
  attachments: [String!]
  type: EmailType!
}
type CompanyEmailTemplate implements EmailTemplate {
  type: CompanyEmailTemplateType!
  fromName: String
  subject: String!
  body: String!
}
type CompanyOnboarding {
  id: ID!
  pendingSteps: [CompanyOnboardingStep]!
  completedSteps: [CompanyOnboardingStep]!
}
type CompanyOnboardingStep {
  stepType: CompanyOnboardingStepType!
  mandatory: Boolean!
  completed: Boolean!
}
type CompanyUser {
  id: ID!
  company: Company!
  name: String!
  email: String!
  phoneNumber: String
  status: CompanyUserStatus!
  roles: [CompanyUserRole]!
}
type ContactDetails {
  contactType: ContactType!
  name: String!
  title: String
  email: String!
  phoneNo: String
}
type CustomTag {
  id: ID
  " to support client dto transformations"
  key: String!
  label: String!
  value: String!
  type: CustomTagType!
  description: String
}
type CustomerAssignment {
  accountManager: CompanyUser
  supportRepresentative: CompanyUser
}
type CustomerBusiness implements Customer {
  id: ID!
  type: CustomerType!
  company: Company!
  status: CustomerStatus!
  stage: CustomerStage!
  basicDetails: BusinessCustomerBasicDetails!
  documents: [Document]!
  assignments: [CustomerAssignment]!
  notes: [Notes]
  customTags: [CustomTag]!
  plainTags: [String!]
}
type CustomerIndividual implements Customer {
  id: ID!
  company: Company!
  type: CustomerType!
  status: CustomerStatus!
  stage: CustomerStage!
  basicDetails: IndividualCustomerBasicDetails!
  documents: [Document]!
  assignments: [CustomerAssignment]!
  notes: [Notes]
  customTags: [CustomTag]!
  plainTags: [String!]
}
type CustomerProduct implements Product {
  id: ID!
  productType: ProductType!
  productCode: String
  "User provided, can be null"
  company: Company!
  customer: Customer!
  name: String!
  description: String
  status: ProductStatus!
  version: ID!
  documents: [Document!]
  customTags: [CustomTag!]
  dimensions: [Dimension!]
  pricing: Pricing!
  quote: Quote
  "or quoteProduct?"
  tax: [Tax!]
}
type Dimension {
  key: String!
  value: String!
}
type Discount {
  id: ID!
  """
   Auto generated
  name: String!
  """
  description: String
  discountType: DiscountType!
  discountValue: DiscountValue!
  discountLevel: DiscountLevel!
}
type DiscountValue {
  value: Amount!
  percentage: Float!
}
type Document {
  id: ID
  file: FileLink
  label: String
  description: String
  tags: [String!]!
}
type FileLink {
  signedReadURL: String
  signedWriteURL: String
}
type IndividualCustomerBasicDetails {
  contactDetails: ContactDetails!
  referralSource: String
  address: String
}
type Margin {
  percentage: Float!
  absoluteAmount: Amount!
}
type MasterProduct implements Product {
  id: ID!
  productType: ProductType!
  productCode: String
  "User provided, can be null"
  company: Company!
  name: String!
  description: String
  status: ProductStatus!
  version: ID!
  documents: [Document!]
  customTags: [CustomTag!]
  dimensions: [Dimension!]
  pricing: Pricing!
  tax: [Tax!]
}
type Mutation {
  ok: Boolean
  masterProductUpsertDetails(input: MasterProductUpsertInput!): MasterProduct!
  masterProductUpsertAdditionalDetails(input: MasterProductUpsertAdditionalDetailsInput!): MasterProduct!
  masterProductToggleActivation(input: MasterProductToggleActivationInput!): MasterProduct!
  customerProductUpdate(input: CustomerProductUpdateInput!): CustomerProduct!
  customerProductToggleActivation(input: CustomerProductToggleActivationInput!): CustomerProduct!
  quoteUpsert(input: QuoteUpsertInput!): Quote!
  companyCreateBasicDetails(input: CompanyCreateBasicDetailsInput!): Company
  companyUpdateBasicDetails(input: CompanyUpdateBasicDetailsInput!): Company
  companyUserCreate(input: CompanyUserCreateInput!): CompanyUser!
  companyUserInvite(companyUserId: ID!): CompanyUser!
  companyUserActivate: CompanyUser!
  companyUserDeactivate(companyUserId: ID!): TaskResponse!
  customerUpsertBasicDetails(input: CustomerUpsertBasicDetailsInput!): Customer
  customerUpsertAdditionalDetails(input: CustomerUpsertAdditionalDetailsInput!): Customer
}
type Notes {
  id: ID
  " to support client dto transformations"
  content: String!
  tags: [String!]!
}
type OplatzEmail implements Email {
  template: OplatzEmailTemplate!
  subject: String
  " optional as the template can support this as well"
  body: String
  " optional as the template can support this as well"
  recipients: [String!]!
  cc: [String!]
  bcc: [String!]
  attachments: [String!]
  type: EmailType!
}
type OplatzEmailTemplate implements EmailTemplate {
  type: OplatzEmailTemplateType!
  fromName: String
  subject: String!
  body: String!
}
type Pricing {
  id: ID!
  chargePolicy: ChargePolicy!
  discount: [Discount]!
  costPrice: Amount!
  " price at which we buy from vendor or cost of rendering the service"
  listPrice: Amount!
  " default price to sell at to customer"
  sellingPrice: Amount!
  " price @ which we sell to customer after discount, will be same as list price by default"
  margin: Margin!
  """
   margin = (sellingPrice - costPrice) / sellingPrice
  billingFrequency
  """
  unit: ProductUnit!
}
type ProductUnit {
  unit: Float!
  " unit of measurement for the product, e.g: 1"
  unitType: String!
}
type Query {
  ok: Boolean
  masterProductsGet(filters: GetMasterProductFilters): [MasterProduct!]!
  masterProductGetById(id: ID!): MasterProduct!
  quotesGet(filters: GetQuotesFilters): [Quote!]!
  getCompany: Company
  getCompanyUser: CompanyUser
  getCustomer(id: ID!): Customer
  getCustomers(filters: GetCustomersQueryFilterInput): [Customer!]!
  _service: _Service!
}
type Quote {
  id: ID!
  description: String
  company: Company!
  customer: Customer!
  assignedCompanyUser: CompanyUser
  "the person example, sales etc to which this quote is assigned."
  products: [QuoteProduct!]!
  status: QuoteStatus!
  createdDate: DateTime!
  version: ID!
  documents: [Document!]
  customTags: [CustomTag!]
  quoteTotalListPrice: Amount!
  " quote products.listPrice"
  quoteTotalSellingPrice: Amount!
  quoteTotalTaxAmount: Amount!
  quoteTotalDiscountAmount: Amount!
  "calculated value from the quote product items"
  discounts: [Discount!]!
  "calculated value from the quote product items"
  assignments: [QuoteAssignment!]
  address: QuoteAddress!
  date: QuoteDate!
  currency: Currency!
}
type QuoteAddress {
  fromAddress: String!
  toAddress: String!
}
type QuoteAssignment {
  salesExecutive: CompanyUser
  customerSuccessManger: CompanyUser
}
type QuoteDate {
  validFrom: DateTime!
  validTill: DateTime
}
type QuoteProduct implements Product {
  id: ID!
  productType: ProductType!
  productCode: String
  "User provided, can be null"
  name: String!
  company: Company!
  quote: Quote!
  description: String
  status: ProductStatus!
  "we don't want that, right? Shall we always keep it CREATED?"
  version: ID!
  documents: [Document!]
  customTags: [CustomTag!]
  dimensions: [Dimension!]
  pricing: Pricing!
  referenceProduct: QuoteProductReferenceProduct
  tax: [Tax!]
}
type QuoteProductReferenceProduct {
  type: ProductType
  id: ID
}
type TaskResponse {
  success: Boolean!
  message: String!
}
type Tax {
  name: String
  percentage: Float!
  amount: Amount!
}
type _Service {
  sdl: String!
}
interface Customer {
  id: ID!
  company: Company!
  status: CustomerStatus!
  stage: CustomerStage!
  type: CustomerType!
  documents: [Document]!
  assignments: [CustomerAssignment]!
  notes: [Notes]
  plainTags: [String!]
  customTags: [CustomTag]!
}
interface Email {
  template: EmailTemplate!
  subject: String
  " optional as the template can support this as well"
  body: String
  " optional as the template can support this as well"
  recipients: [String!]!
  cc: [String!]
  bcc: [String!]
  attachments: [String!]
  type: EmailType!
}
interface EmailTemplate {
  subject: String!
  body: String!
  fromName: String
}
interface Product {
  id: ID!
  company: Company!
  productCode: String
  " User provided, unique identifier along with dimensions, it can be null"
  name: String!
  " Will be used as unique identifier along with dimensions, if productCode is null"
  description: String
  status: ProductStatus!
  version: ID!
  dimensions: [Dimension!]
  documents: [Document!]
  customTags: [CustomTag!]
  pricing: Pricing!
  productType: ProductType!
  tax: [Tax!]
}
enum ChargePolicy {
  UNIT
}
enum CompanyEmailTemplateType {
  COMPANY_WELCOME_EMAIL
  COMPANY_USER_INVITATION_EMAIL
}
enum CompanyOnboardingStepType {
  BASIC_DETAILS
  ACCOUNTING_DETAILS
  CATALOG_DETAILS
  COMPANY_USERS_DETAILS
}
enum CompanyStatus {
  CREATED
  ONBOARDING
  ACTIVE
  INACTIVE
}
enum CompanyUserRole {
  ADMIN
  CUSTOMER_SUCCESS_MANAGER
  ACCOUNT_EXECUTIVE
  CHARTERED_ACCOUNTANT
}
enum CompanyUserStatus {
  CREATED
  INVITED
  ACTIVE
  INACTIVE
}
enum ContactType {
  PERSON
  BUSINESS
}
enum Currency {
  USD
  INR
  AED
}
enum CustomTagType {
  STRING
  NUMERIC
  DATE
  SELECT
  BOOLEAN
}
enum CustomerStage {
  LEAD
  PROSPECT
  CONVERSION_IN_PROGRESS
  CONVERTED
  CONVERSION_FAILED
  CHURNED
}
enum CustomerStatus {
  ACTIVE
  SUSPENDED
}
enum CustomerType {
  BUSINESS
  INDIVIDUAL
}
enum DiscountLevel {
  PRODUCT
  QUOTE
}
enum DiscountType {
  PERCENTAGE
  AMOUNT
}
enum EmailType {
  COMPANY_EMAIL
  OPLATZ_EMAIL
}
enum EntitySize {
  LESS_THAN_TEN
  TEN_TO_HUNDRED
  HUNDRED_TO_THOUSAND
  GREATER_THAN_THOUSAND
  UNKNOWN
}
enum ErrorDetail {
  """
  Unknown error.
  
  This error should only be returned when no other error detail applies.
  If a client sees an unknown errorDetail, it will be interpreted as UNKNOWN.
  
  HTTP Mapping: 500 Internal Server Error
  """
  UNKNOWN
  """
  The requested field is not found in the schema.
  
  This differs from `NOT_FOUND` in that `NOT_FOUND` should be used when a
  query is valid, but is unable to return a result (if, for example, a
  specific video id doesn't exist). `FIELD_NOT_FOUND` is intended to be
  returned by the server to signify that the requested field is not known to exist.
  This may be returned in lieu of failing the entire query.
  See also `PERMISSION_DENIED` for cases where the
  requested field is invalid only for the given user or class of users.
  
  HTTP Mapping: 404 Not Found
  Error Type: BAD_REQUEST
  """
  FIELD_NOT_FOUND
  """
  The provided cursor is not valid.
  
  The most common usage for this error is when a client is paginating
  through a list that uses stateful cursors. In that case, the provided
  cursor may be expired.
  
  HTTP Mapping: 404 Not Found
  Error Type: NOT_FOUND
  """
  INVALID_CURSOR
  """
  The operation is not implemented or is not currently supported/enabled.
  
  HTTP Mapping: 501 Not Implemented
  Error Type: BAD_REQUEST
  """
  UNIMPLEMENTED
  """
  The client specified an invalid argument.
  
  Note that this differs from `FAILED_PRECONDITION`.
  `INVALID_ARGUMENT` indicates arguments that are problematic
  regardless of the state of the system (e.g., a malformed file name).
  
  HTTP Mapping: 400 Bad Request
  Error Type: BAD_REQUEST
  """
  INVALID_ARGUMENT
  """
  The deadline expired before the operation could complete.
  
  For operations that change the state of the system, this error
  may be returned even if the operation has completed successfully.
  For example, a successful response from a server could have been
  delayed long enough for the deadline to expire.
  
  HTTP Mapping: 504 Gateway Timeout
  Error Type: UNAVAILABLE
  """
  DEADLINE_EXCEEDED
  """
  Service Error.
  
  There is a problem with an upstream service.
  
  This may be returned if a gateway receives an unknown error from a service
  or if a service is unreachable.
  If a request times out which waiting on a response from a service,
  `DEADLINE_EXCEEDED` may be returned instead.
  If a service returns a more specific error Type, the specific error Type may
  be returned instead.
  
  HTTP Mapping: 502 Bad Gateway
  Error Type: UNAVAILABLE
  """
  SERVICE_ERROR
  """
  Request throttled based on server CPU limits
  
  HTTP Mapping: 503 Unavailable.
  Error Type: UNAVAILABLE
  """
  THROTTLED_CPU
  """
  Request throttled based on server concurrency limits.
  
  HTTP Mapping: 503 Unavailable
  Error Type: UNAVAILABLE
  """
  THROTTLED_CONCURRENCY
  """
  The server detected that the client is exhibiting a behavior that
  might be generating excessive load.
  
  HTTP Mapping: 420 Enhance Your Calm
  Error Type: UNAVAILABLE
  """
  ENHANCE_YOUR_CALM
  """
  The server detected that the client is exhibiting a behavior that
  might be generating excessive load.
  
  HTTP Mapping: 429 Too Many Requests
  Error Type: UNAVAILABLE
  """
  TOO_MANY_REQUESTS
  """
  Request failed due to network errors.
  
  HTTP Mapping: 503 Unavailable
  Error Type: UNAVAILABLE
  """
  TCP_FAILURE
  """
  Unable to perform operation because a required resource is missing.
  
  Example: Client is attempting to refresh a list, but the specified
  list is expired. This requires an action by the client to get a new list.
  
  If the user is simply trying GET a resource that is not found,
  use the NOT_FOUND error type. FAILED_PRECONDITION.MISSING_RESOURCE
  is to be used particularly when the user is performing an operation
  that requires a particular resource to exist.
  
  HTTP Mapping: 400 Bad Request or 500 Internal Server Error
  Error Type: FAILED_PRECONDITION
  """
  MISSING_RESOURCE
}
enum ErrorType {
  """
  Unknown error.
  
  For example, this error may be returned when
  an error code received from another address space belongs to
  an error space that is not known in this address space.  Also
  errors raised by APIs that do not return enough error information
  may be converted to this error.
  
  If a client sees an unknown errorType, it will be interpreted as UNKNOWN.
  Unknown errors MUST NOT trigger any special behavior. These MAY be treated
  by an implementation as being equivalent to INTERNAL.
  
  When possible, a more specific error should be provided.
  
  HTTP Mapping: 520 Unknown Error
  """
  UNKNOWN
  """
  Internal error.
  
  An unexpected internal error was encountered. This means that some
  invariants expected by the underlying system have been broken.
  This error code is reserved for serious errors.
  
  HTTP Mapping: 500 Internal Server Error
  """
  INTERNAL
  """
  The requested entity was not found.
  
  This could apply to a resource that has never existed (e.g. bad resource id),
  or a resource that no longer exists (e.g. cache expired.)
  
  Note to server developers: if a request is denied for an entire class
  of users, such as gradual feature rollout or undocumented allowlist,
  `NOT_FOUND` may be used. If a request is denied for some users within
  a class of users, such as user-based access control, `PERMISSION_DENIED`
  must be used.
  
  HTTP Mapping: 404 Not Found
  """
  NOT_FOUND
  """
  The request does not have valid authentication credentials.
  
  This is intended to be returned only for routes that require
  authentication.
  
  HTTP Mapping: 401 Unauthorized
  """
  UNAUTHENTICATED
  """
  The caller does not have permission to execute the specified
  operation.
  
  `PERMISSION_DENIED` must not be used for rejections
  caused by exhausting some resource or quota.
  `PERMISSION_DENIED` must not be used if the caller
  cannot be identified (use `UNAUTHENTICATED`
  instead for those errors).
  
  This error Type does not imply the
  request is valid or the requested entity exists or satisfies
  other pre-conditions.
  
  HTTP Mapping: 403 Forbidden
  """
  PERMISSION_DENIED
  """
  Bad Request.
  
  There is a problem with the request.
  Retrying the same request is not likely to succeed.
  An example would be a query or argument that cannot be deserialized.
  
  HTTP Mapping: 400 Bad Request
  """
  BAD_REQUEST
  """
  Currently Unavailable.
  
  The service is currently unavailable.  This is most likely a
  transient condition, which can be corrected by retrying with
  a backoff.
  
  HTTP Mapping: 503 Unavailable
  """
  UNAVAILABLE
  """
  The operation was rejected because the system is not in a state
  required for the operation's execution.  For example, the directory
  to be deleted is non-empty, an rmdir operation is applied to
  a non-directory, etc.
  
  Service implementers can use the following guidelines to decide
  between `FAILED_PRECONDITION` and `UNAVAILABLE`:
  
  - Use `UNAVAILABLE` if the client can retry just the failing call.
  - Use `FAILED_PRECONDITION` if the client should not retry until
  the system state has been explicitly fixed.  E.g., if an "rmdir"
       fails because the directory is non-empty, `FAILED_PRECONDITION`
  should be returned since the client should not retry unless
  the files are deleted from the directory.
  
  HTTP Mapping: 400 Bad Request or 500 Internal Server Error
  """
  FAILED_PRECONDITION
}
enum OplatzEmailTemplateType {
  OPLATZ_COMPANY_CREATED
}
enum ProductStatus {
  ACTIVE
  INACTIVE
  CREATED
}
enum ProductType {
  MASTER
  QUOTE
  CUSTOMER
}
enum QuoteStatus {
  CREATED
  SENT
  ACCEPTED
  REJECTED
  ACTIVATED
  DELETED
}
input AmountInput {
  value: Float!
  currency: Currency!
}
" Company user ids"
input AssignmentInput {
  accountManager: ID
  supportRepresentative: ID
}
input BusinessCustomerBasicDetailsInput {
  legalName: String!
  website: String!
  size: EntitySize!
  industry: String
  referralSource: String
  address: String
  contactPersonDetails: ContactPersonDetailsInput!
}
input CompanyCreateBasicDetailsInput {
  name: String!
  address: String
  phoneNumber: String
  email: String!
  website: String!
  industry: String
  size: EntitySize
}
input CompanyUpdateBasicDetailsInput {
  companyId: ID!
  address: String
  phoneNumber: String
  website: String!
  industry: String
  size: EntitySize
}
" input financial settings"
input CompanyUserCreateInput {
  companyId: ID!
  name: String!
  email: String!
  roles: [CompanyUserRole!]!
}
input ContactPersonDetailsInput {
  name: String!
  title: String
  email: String!
  phoneNo: String
  contactType: ContactType!
}
input CustomTagInput {
  id: ID
  key: String!
  label: String!
  value: String!
  type: CustomTagType!
  description: String
}
input CustomerProductToggleActivationInput {
  id: ID!
  version: ID
}
input CustomerProductUpdateInput {
  id: ID!
  productCode: String
  " User provided unique identifier, can be null"
  customerId: ID!
  " Required to identify the customer"
  quoteId: ID!
  "To the code it's tagged to."
  name: String!
  " Required as unique identifier along with dimensions"
  description: String
  dimensions: [DimensionInput!]
  " Product dimensions/variants"
  documents: [DocumentInput!]
  " Associated documents"
  customTags: [CustomTagInput!]
  " Custom tags for categorization"
  pricing: PricingInput!
  " Pricing information"
  discount: DiscountInput!
}
input CustomerUpsertAdditionalDetailsInput {
  customerId: ID!
  assignments: [AssignmentInput!]
  notes: [NotesInput!]
  documents: [DocumentInput!]
  customTags: [CustomTagInput!]
}
input CustomerUpsertBasicDetailsInput {
  customerId: ID
  customerType: CustomerType!
  customerStage: CustomerStage!
  individualCustomerDetails: IndividualCustomerBasicDetailsInput
  businessCustomerDetails: BusinessCustomerBasicDetailsInput
}
input DimensionInput {
  key: String!
  value: String!
}
input DiscountInput {
  discountValue: DiscountValueInput!
  discountType: DiscountType!
  discountLevel: DiscountLevel!
}
input DiscountValueInput {
  amount: AmountInput!
  percentage: Float!
}
input DocumentInput {
  id: ID
  file: FileLinkInput
}
input FileLinkInput {
  signedReadURL: String
  signedWriteURL: String
}
input GetCustomersQueryFilterInput {
  status: CustomerStatus
  stage: CustomerStage
  customerType: CustomerType
}
input GetMasterProductFilters {
  status: ProductStatus
}
input GetQuotesFilters {
  status: QuoteStatus
}
input IndividualCustomerBasicDetailsInput {
  fullName: String!
  emailAddress: String!
  phoneNumber: String
  referralSource: String
  address: String
}
input MarginInput {
  absoluteAmount: AmountInput!
  percentage: Float!
}
input MasterProductToggleActivationInput {
  id: ID!
  version: ID
}
input MasterProductUpsertAdditionalDetailsInput {
  masterProductId: ID!
  documents: [DocumentInput!]
  customTags: [CustomTagInput!]
}
input MasterProductUpsertInput {
  id: ID
  productCode: String
  " will be computed in the backend if null"
  name: String!
  description: String
  dimensions: [DimensionInput!]
  pricing: PricingInput!
}
input NotesInput {
  id: ID
  content: String!
  tags: [String!]!
}
input PricingInput {
  id: ID
  chargePolicy: ChargePolicy!
  costPrice: AmountInput!
  " price at which we buy from vendor or cost of rendering the service"
  listPrice: AmountInput!
  " default price to sell at to customer"
  sellingPrice: AmountInput!
  " price @ which we sell to customer after discount, will be same as list price by default"
  productUnit: ProductUnitInput!
  " unit of measurement for the product, e.g: 1"
  discounts: [DiscountInput!]
  " discounts applicable on the product, e.g: 10% off"
  tax: [TaxInput!]
}
input ProductUnitInput {
  unit: Float!
  " unit of measurement for the product, e.g: 1"
  unitType: String!
}
input QuoteAddressInput {
  fromAddress: String!
  toAddress: String!
}
input QuoteAssignmentInput {
  salesExecutive: ID
  customerSuccessManger: ID
}
input QuoteDateInput {
  validFrom: Date!
  validTill: Date
}
input QuoteProductCreateInput {
  productCode: String
  referenceProduct: QuoteProductReferenceProductInput
  "non mandatory."
  name: String!
  customerId: ID!
  description: String
  dimensions: [DimensionInput!]
  pricing: PricingInput!
  documents: [DocumentInput!]
  customTags: [CustomTagInput!]
  tax: [TaxInput!]
}
input QuoteProductReferenceProductInput {
  type: ProductType!
  id: ID!
}
input QuoteUpsertInput {
  id: ID
  description: String
  customerId: ID!
  quoteProductsInput: [QuoteProductCreateInput!]!
  paymentTerms: String
  discounts: [DiscountInput!]
  documents: [DocumentInput!]
  customTags: [CustomTagInput!]
  assignments: [QuoteAssignmentInput!]
  addressInput: QuoteAddressInput!
  dateInput: QuoteDateInput!
  currency: Currency!
}
input TaxInput {
  name: String
  percentage: Float!
  amount: AmountInput!
}
directive @extends on OBJECT | INTERFACE
directive @external on FIELD_DEFINITION
directive @key(fields: _FieldSet!) on OBJECT | INTERFACE
directive @provides(fields: _FieldSet!) on FIELD_DEFINITION
directive @requires(fields: _FieldSet!) on FIELD_DEFINITION
"Exposes a URL that specifies the behaviour of this scalar."
directive @specifiedBy(
    "The URL that specifies the behaviour of this scalar."
    url: String!
  ) on SCALAR
"Indicates an Input Object is a OneOf Input Object."
directive @oneOf on INPUT_OBJECT
"This directive allows results to be deferred during execution"
directive @defer(
    "Deferred behaviour is controlled by this argument"
    if: Boolean! = true,
    "A unique label that represents the fragment being deferred"
    label: String
  ) on FRAGMENT_SPREAD | INLINE_FRAGMENT
"This directive disables error propagation when a non nullable field returns null for the given operation."
directive @experimental_disableErrorPropagation on QUERY | MUTATION | SUBSCRIPTION